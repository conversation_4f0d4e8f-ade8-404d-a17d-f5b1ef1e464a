#ifndef _MEMLAYOUT_H_
#define _MEMLAYOUT_H_

// 内存布局常量
#define EXTMEM      0x100000            // 扩展内存开始地址
#define PHYSTOP     0xE000000           // 物理内存顶部
#define DEVSPACE    0xFE000000          // 设备空间

// 内核在物理内存中的位置
#define KERNBASE    0x80000000          // 内核虚拟地址的起始位置
#define KERNLINK    (KERNBASE+EXTMEM)   // 内核链接地址

#define V2P(a) (((uint) (a)) - KERNBASE)
#define P2V(a) ((void *)(((char *) (a)) + KERNBASE))

#define V2P_WO(x) ((x) - KERNBASE)    // 与V2P相同，但不返回指针
#define P2V_WO(x) ((x) + KERNBASE)    // 与P2V相同，但不返回指针

#endif // _MEMLAYOUT_H_
