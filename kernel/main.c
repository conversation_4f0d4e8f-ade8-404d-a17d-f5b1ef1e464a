#include "../include/types.h"
#include "../include/defs.h"
#include "../include/x86.h"
#include "../include/uart.h"

// 内核主函数
void kmain(void)
{
  // 初始化控制台
  consoleinit();

  // 输出Hello World
  cprintf("Hello World from MyOS!\n");

  // 初始化串行端口
  uartinit();

  cprintf("Type something in the terminal: \n");
  cprintf("Waiting for input...\n");

  // 无限循环，轮询串行输入
  for (;;)
  {
    // 检查串行端口状态
    uint uart_status = inb(COM1 + UART_LSR);
    if ((uart_status & UART_LSR_DR) != 0)
    {
      cprintf("UART status: 0x%x (data available)\n", uart_status);
    }

    // 轮询串行输入
    uartpoll();
  }
}
